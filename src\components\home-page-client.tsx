'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { AuthModal } from '@/components/auth/auth-modal'
import { FileText, Search, Lightbulb } from 'lucide-react'
import { FloatingSocialShare } from '@/components/social/social-share'
import { HomeTrialSection } from '@/components/home/<USER>'
import { IntroVideoSection } from '@/components/home/<USER>'
import { ScrollFeaturesSection } from '@/components/home/<USER>'
import DocumentCarouselSection from '@/components/home/<USER>'
import FeaturesList from '@/components/features/FeaturesList'

import { useTranslation } from '@/hooks/use-translation'

// Dynamic text switcher component
function DynamicDocumentType() {
  const { t, isReady } = useTranslation('homepage')

  // Fallback document types if translation not ready
  const fallbackTypes = [
    "financial reports",
    "legal documents",
    "insurance policies",
    "request for proposals",
    "research papers",
    "compliance documents"
  ]

  // Helper function to safely get array from translation
  const getDocumentTypes = (): string[] => {
    if (!isReady) return fallbackTypes
    const value = t('hero.documentTypes')
    return Array.isArray(value) ? value : fallbackTypes
  }

  const documentTypes = getDocumentTypes()

  const [currentIndex, setCurrentIndex] = useState(0)
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    const interval = setInterval(() => {
      setIsVisible(false)

      setTimeout(() => {
        setCurrentIndex((prev) => (prev + 1) % documentTypes.length)
        setIsVisible(true)
      }, 300) // Half of transition duration
    }, 3000) // Change every 3 seconds

    return () => clearInterval(interval)
  }, [documentTypes.length])

  return (
    <span
      className={`bg-gradient-to-r from-purple-600 via-blue-600 to-pink-600 bg-clip-text text-transparent transition-all duration-500 ease-in-out ${
        isVisible ? 'opacity-100 transform translate-y-0' : 'opacity-0 transform -translate-y-2'
      }`}
    >
      {documentTypes[currentIndex]}?
    </span>
  )
}

export function HomePageClient() {
  const { t } = useTranslation('homepage')
  const [authModal, setAuthModal] = useState({
    isOpen: false,
    mode: 'signin' as 'signin' | 'signup'
  })

  const handleGetStarted = () => {
    setAuthModal({ isOpen: true, mode: 'signin' })
  }

  const handleGetStartedToday = () => {
    setAuthModal({ isOpen: true, mode: 'signup' })
  }

  return (
    <>
      {/* Special Offer Banner */}
      <div className="bg-yellow-100 border-b border-yellow-200 py-2 sm:py-3">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <p className="text-center text-yellow-800 font-medium text-xs xs:text-sm sm:text-base md:text-base lg:text-base">
            {t('banner.limitedOffer')}
          </p>
        </div>
      </div>

      <main className="flex flex-col">
        {/* Hero Section */}
        <section className="relative bg-gradient-to-br from-purple-50 via-blue-50 to-pink-50 py-12 sm:py-16 md:py-20 lg:py-28 xl:py-32 min-h-[70vh] sm:min-h-[75vh] md:min-h-[80vh] flex items-center" role="banner">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-10 gap-8 lg:gap-12 items-center">
              {/* Left Container - 70% */}
              <div className="lg:col-span-7">
                <div className="text-left">
                  <h1 className="text-3xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-5xl xl:text-5xl font-bold text-gray-900 mb-3 sm:mb-4 md:mb-5 leading-tight">
                    <span className="block">{t('hero.title.line1')}</span>
                    <span className="block">{t('hero.title.line2')} <DynamicDocumentType /></span>
                  </h1>

                  <h2 className="text-lg xs:text-xl sm:text-2xl md:text-xl lg:text-xl xl:text-xl font-medium text-gray-700 mb-2 sm:mb-3 md:mb-4 leading-relaxed">
                    {t('hero.subtitle')}
                  </h2>

                  <h3 className="text-sm xs:text-base sm:text-lg md:text-sm lg:text-sm xl:text-sm text-gray-600 mb-6 sm:mb-7 md:mb-8 leading-relaxed">
                    {t('hero.description')}
                  </h3>
                  <div className="flex flex-col gap-3 sm:gap-4 items-start">
                    <Link href="/pricing" aria-label="View pricing plans for DocuChampAI" title="View pricing plans for DocuChampAI">
                      <Button
                        variant="outline"
                        size="lg"
                        className="border-purple-300 text-purple-700 hover:bg-purple-50 w-48 xs:w-52 sm:w-60 px-6 xs:px-7 sm:px-8 py-3 xs:py-3.5 sm:py-4 text-base xs:text-lg"
                      >
                        {t('hero.buttons.viewPricing')}
                      </Button>
                    </Link>
                    <Button
                      onClick={handleGetStarted}
                      size="lg"
                      className="text-white transition-all duration-300 transform hover:scale-105 !rounded-md bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 shadow-lg hover:shadow-purple-500/50 w-48 xs:w-52 sm:w-60 px-6 xs:px-7 sm:px-8 py-3 xs:py-3.5 sm:py-4 text-base xs:text-lg"
                      aria-label="Get started with DocuChampAI for free"
                    >
                      {t('hero.buttons.getStartedFree')}
                    </Button>
                  </div>
                </div>
              </div>

              {/* Right Container - 30% */}
              <div className="lg:col-span-3">
                <FeaturesList />
              </div>
            </div>
          </div>
        </section>

        {/* Document Carousel Section */}
        <DocumentCarouselSection />

        {/* Scroll Features Section */}
        <ScrollFeaturesSection />

        {/* Intro Video Section */}
        <IntroVideoSection />

        {/* Trial Section */}
        <HomeTrialSection />

      {/* Why DocuChampAI Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold text-gray-900 mb-6">
              {t('whySection.title')}
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              {t('whySection.description')}
            </p>
          </div>

          {/* Constrain table width and center it */}
          <div className="max-w-5xl mx-auto">
            <div className="bg-gradient-to-br from-slate-50 via-white to-purple-50 rounded-3xl p-4 sm:p-8 shadow-2xl border border-purple-100">
              <div className="overflow-x-auto">
                <table className="w-full table-auto">
                  <thead>
                    <tr className="border-b-2 border-gradient-to-r from-purple-200 to-pink-200">
                      <th className="w-1/4 text-left py-3 sm:py-6 px-2 sm:px-6 text-gray-700 font-bold text-sm sm:text-lg">{t('whySection.comparisonTable.headers.feature')}</th>
                      <th className="w-1/3 text-center py-3 sm:py-6 px-2 sm:px-6 text-gray-700 font-bold text-sm sm:text-lg">{t('whySection.comparisonTable.headers.others')}</th>
                      <th className="w-5/12 text-center py-3 sm:py-6 px-2 sm:px-6 text-transparent bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text font-bold text-sm sm:text-lg">{t('whySection.comparisonTable.headers.docuchamp')}</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-purple-100">
                    <tr className="hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 transition-all duration-300">
                      <td className="py-4 sm:py-8 px-2 sm:px-6 font-semibold text-gray-900 text-xs sm:text-base">{t('whySection.comparisonTable.rows.dataUnderstanding.feature')}</td>
                      <td className="py-4 sm:py-8 px-2 sm:px-6 text-center text-gray-600 text-xs sm:text-base leading-relaxed">{t('whySection.comparisonTable.rows.dataUnderstanding.others')}</td>
                      <td className="py-4 sm:py-8 px-2 sm:px-6 text-center text-purple-700 font-semibold text-xs sm:text-base leading-relaxed">{t('whySection.comparisonTable.rows.dataUnderstanding.docuchamp')}</td>
                    </tr>
                    <tr className="hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 transition-all duration-300">
                      <td className="py-4 sm:py-8 px-2 sm:px-6 font-semibold text-gray-900 text-xs sm:text-base">{t('whySection.comparisonTable.rows.documentSize.feature')}</td>
                      <td className="py-4 sm:py-8 px-2 sm:px-6 text-center text-gray-600 text-xs sm:text-base leading-relaxed">{t('whySection.comparisonTable.rows.documentSize.others')}</td>
                      <td className="py-4 sm:py-8 px-2 sm:px-6 text-center text-purple-700 font-semibold text-xs sm:text-base leading-relaxed">{t('whySection.comparisonTable.rows.documentSize.docuchamp')}</td>
                    </tr>
                    <tr className="hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 transition-all duration-300">
                      <td className="py-4 sm:py-8 px-2 sm:px-6 font-semibold text-gray-900 text-xs sm:text-base">{t('whySection.comparisonTable.rows.useCase.feature')}</td>
                      <td className="py-4 sm:py-8 px-2 sm:px-6 text-center text-gray-600 text-xs sm:text-base leading-relaxed">{t('whySection.comparisonTable.rows.useCase.others')}</td>
                      <td className="py-4 sm:py-8 px-2 sm:px-6 text-center text-purple-700 font-semibold text-xs sm:text-base leading-relaxed">{t('whySection.comparisonTable.rows.useCase.docuchamp')}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* AI Multimodal Feature Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Content */}
            <div className="lg:order-1">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                {t('aiFeatures.title')}
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                {t('aiFeatures.description')}
              </p>

              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <Search className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">{t('aiFeatures.featureList.chartAnalysis.title')}</h3>
                    <p className="text-gray-600">{t('aiFeatures.featureList.chartAnalysis.description')}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <Lightbulb className="w-4 h-4 text-orange-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">{t('aiFeatures.featureList.tableExtraction.title')}</h3>
                    <p className="text-gray-600">{t('aiFeatures.featureList.tableExtraction.description')}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <Search className="w-4 h-4 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">{t('aiFeatures.featureList.imageRecognition.title')}</h3>
                    <p className="text-gray-600">{t('aiFeatures.featureList.imageRecognition.description')}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <FileText className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">{t('aiFeatures.featureList.textAnalysis.title')}</h3>
                    <p className="text-gray-600">{t('aiFeatures.featureList.textAnalysis.description')}</p>
                  </div>
                </div>
              </div>

              <div className="mt-8 p-4 bg-white rounded-lg border border-gray-200">
                <div className="flex items-center justify-between text-sm text-gray-600">
                  <span>{t('aiFeatures.supportInfo.pages')}</span>
                  <span>{t('aiFeatures.supportInfo.formats')}</span>
                </div>
              </div>
            </div>

            {/* Video */}
            <div className="lg:order-2 flex justify-center">
              <div className="relative max-w-md">
                <video
                  src="/AI Read Everything.webm"
                  autoPlay
                  muted
                  loop
                  playsInline
                  className="w-full h-auto rounded-lg shadow-lg"
                  aria-label="AI Reading Everything - Document Analysis Demo"
                >
                  <source src="/AI Read Everything.webm" type="video/webm" />
                  Your browser does not support the video tag.
                </video>
                <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent rounded-lg pointer-events-none"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-purple-900 via-blue-900 to-pink-900 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold text-white mb-4">
            {t('cta.title')}
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            {t('cta.description')}
          </p>
          <Button
            onClick={handleGetStartedToday}
            size="lg"
            className="text-white transition-all duration-300 transform hover:scale-105 !rounded-md bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 shadow-lg hover:shadow-purple-500/50 w-full sm:w-auto max-w-xs mx-auto"
          >
            {t('cta.button')}
          </Button>
        </div>
      </section>
      </main>

      {/* Floating Social Share for Mobile */}
      <FloatingSocialShare
        title="DocuChampAI - AI Document Analysis & Automation"
        description="Transform your document workflow with AI. Extract data from PDFs, analyze charts & tables, automate reports."
      />

      {/* Auth Modal */}
      <AuthModal
        isOpen={authModal.isOpen}
        onClose={() => setAuthModal({ ...authModal, isOpen: false })}
        initialMode={authModal.mode}
      />
    </>
  )
}
