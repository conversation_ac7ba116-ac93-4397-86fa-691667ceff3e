'use client'

import { useTranslation } from '@/hooks/use-translation'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

export default function FeaturesList() {
  const { t } = useTranslation('homepage')

  const features = [
    {
      key: 'explainingDiagrams',
      title: t('features.explainingDiagrams.title'),
      description: t('features.explainingDiagrams.description')
    },
    {
      key: 'autoReportGeneration',
      title: t('features.autoReportGeneration.title'),
      description: t('features.autoReportGeneration.description')
    },
    {
      key: 'handwritingReading',
      title: t('features.handwritingReading.title'),
      description: t('features.handwritingReading.description')
    },
    {
      key: 'translation',
      title: t('features.translation.title'),
      description: t('features.translation.description')
    },
    {
      key: 'formChecking',
      title: t('features.formChecking.title'),
      description: t('features.formChecking.description')
    },
    {
      key: 'mismatchFinding',
      title: t('features.mismatchFinding.title'),
      description: t('features.mismatchFinding.description')
    },
    {
      key: 'documentSummarizing',
      title: t('features.documentSummarizing.title'),
      description: t('features.documentSummarizing.description')
    },
    {
      key: 'autoDataEntry',
      title: t('features.autoDataEntry.title'),
      description: t('features.autoDataEntry.description')
    },
    {
      key: 'mistakeFinding',
      title: t('features.mistakeFinding.title'),
      description: t('features.mistakeFinding.description')
    },
    {
      key: 'keyInformationExtraction',
      title: t('features.keyInformationExtraction.title'),
      description: t('features.keyInformationExtraction.description')
    },
    {
      key: 'contractAnalysis',
      title: t('features.contractAnalysis.title'),
      description: t('features.contractAnalysis.description')
    }
  ]

  return (
    <div className="bg-white/90 backdrop-blur-md rounded-3xl p-6 shadow-xl border border-white/30">
      <h3 className="text-lg font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-5">
        {t('features.title')}
      </h3>
      
      {/* Scrollable Container - Fixed Height */}
      <div className="h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-purple-300 scrollbar-track-gray-100 pr-2">
        <div className="space-y-4">
          {features.map((feature) => (
            <div 
              key={feature.key}
              className="rounded-xl p-4 bg-gray-50/80 border border-gray-200/50 hover:bg-gray-100/80 transition-colors"
            >
              <div className="flex items-center space-x-2 mb-2">
                <div className="w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full"></div>
                <h4 className="font-medium text-gray-800 text-sm">{feature.title}</h4>
              </div>
              <p className="text-xs text-gray-600 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
      
      {/* Scroll Indicator */}
      <div className="mt-3 text-center">
        <div className="inline-flex items-center space-x-1 text-xs text-gray-500">
          <span>{t('features.scrollForMore')}</span>
          <svg className="w-3 h-3 animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </div>

      {/* Contact Section */}
      <div className="mt-6 pt-4 border-t border-gray-200/50 text-center">
        <p className="text-sm text-gray-600 mb-3">
          {t('features.wantMoreFeatures')}
        </p>
        <Link href="/contact" aria-label="Contact us for more features">
          <Button
            variant="outline"
            size="sm"
            className="border-purple-300 text-purple-700 hover:bg-purple-50 hover:border-purple-400 transition-all duration-200"
          >
            {t('features.contactUs')}
          </Button>
        </Link>
      </div>
    </div>
  )
}
