@import "tailwindcss";
@import "../css/embla.css";

/* Prevent horizontal overflow */
html, body {
  overflow-x: hidden;
  max-width: 100vw;
}

/* Modal styles to ensure proper layering */
.auth-modal-backdrop {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 9999 !important;
  background: rgba(0, 0, 0, 0.5) !important;
  backdrop-filter: blur(4px) !important;
}

.auth-modal-content {
  position: relative !important;
  z-index: 10000 !important;
  background: white !important;
  border-radius: 1rem !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
}

:root {
  --background: 0 0% 100%;
  --foreground: 0 0% 3.9%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 3.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 3.9%;
  --primary: 270 91% 65%;
  --primary-foreground: 0 0% 98%;
  --secondary: 0 0% 96.1%;
  --secondary-foreground: 0 0% 9%;
  --muted: 0 0% 96.1%;
  --muted-foreground: 0 0% 45.1%;
  --accent: 0 0% 96.1%;
  --accent-foreground: 0 0% 9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  --border: 0 0% 89.8%;
  --input: 0 0% 89.8%;
  --ring: 270 91% 65%;
  --radius: 0.5rem;
}

.dark {
  --background: 0 0% 3.9%;
  --foreground: 0 0% 98%;
  --card: 0 0% 3.9%;
  --card-foreground: 0 0% 98%;
  --popover: 0 0% 3.9%;
  --popover-foreground: 0 0% 98%;
  --primary: 270 91% 65%;
  --primary-foreground: 0 0% 9%;
  --secondary: 0 0% 14.9%;
  --secondary-foreground: 0 0% 98%;
  --muted: 0 0% 14.9%;
  --muted-foreground: 0 0% 63.9%;
  --accent: 0 0% 14.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --border: 0 0% 14.9%;
  --input: 0 0% 14.9%;
  --ring: 270 91% 65%;
}

* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

/* Prevent zoom blackout issues on mobile */
@media (max-width: 1024px) {
  .mobile-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: rgba(0, 0, 0, 0.5) !important;
    z-index: 30 !important;
    touch-action: manipulation !important;
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    user-select: none !important;
  }
}

/* Markdown viewer table improvements */
.prose table {
  border-collapse: separate !important;
  border-spacing: 0 !important;
}

.prose table td {
  word-wrap: break-word !important;
  word-break: break-word !important;
  max-width: 200px !important;
  overflow-wrap: break-word !important;
}

.prose table th {
  word-wrap: break-word !important;
  word-break: break-word !important;
  max-width: 200px !important;
  overflow-wrap: break-word !important;
}

/* Ensure proper spacing in markdown content */
.prose > * + * {
  margin-top: 1.5rem !important;
}

.prose hr {
  margin-top: 2rem !important;
  margin-bottom: 2rem !important;
}

/* Gradient animation for Professional package */
@keyframes gradient-x {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Glow effect animation */
@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(168, 85, 247, 0.5), 0 0 40px rgba(236, 72, 153, 0.3);
  }
}

/* Pulse border animation */
@keyframes pulse-border {
  0%, 100% {
    border-color: rgba(168, 85, 247, 0.5);
  }
  50% {
    border-color: rgba(168, 85, 247, 0.8);
  }
}

/* Shimmer effect */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Floating animation for cards */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* Scroll-triggered fade in animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Smooth scale animation */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Pricing card hover effects */
.pricing-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.pricing-card.group:hover {
  transform: translateY(-8px) scale(1.02);
}

/* Ensure buttons maintain rounded corners on hover */
.pricing-card button,
.pricing-card button:hover,
.pricing-card button:focus,
.pricing-card button:active {
  border-radius: 0.375rem !important; /* equivalent to rounded-md */
}

/* Force rounded corners for all button states with higher specificity */
.pricing-card .rounded-md,
.pricing-card .rounded-md:hover,
.pricing-card .rounded-md:focus,
.pricing-card .rounded-md:active {
  border-radius: 0.375rem !important;
}

/* Ultra-specific rule to force rounded corners on pricing buttons */
.pricing-card button.force-rounded,
.pricing-card button.force-rounded:hover,
.pricing-card button.force-rounded:focus,
.pricing-card button.force-rounded:active {
  border-radius: 0.375rem !important;
  border-top-left-radius: 0.375rem !important;
  border-top-right-radius: 0.375rem !important;
  border-bottom-left-radius: 0.375rem !important;
  border-bottom-right-radius: 0.375rem !important;
}

/* Professional card special glow */
.professional-glow {
  position: relative;
}

.professional-glow::before {
  content: '';
  position: absolute;
  inset: -2px;
  padding: 2px;
  background: linear-gradient(45deg, #8b5cf6, #ec4899, #8b5cf6);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  animation: gradient-x 3s ease infinite;
  background-size: 200% 200%;
}



/* Enterprise card subtle glow */
.enterprise-glow {
  box-shadow: 0 0 30px rgba(75, 85, 99, 0.3);
  transition: box-shadow 0.3s ease;
}

.enterprise-glow:hover {
  box-shadow: 0 0 40px rgba(75, 85, 99, 0.5), 0 0 60px rgba(156, 163, 175, 0.3);
}

/* Card border animations */
.border-animate {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Embla Carousel Styles */
.embla {
  max-width: 48rem;
  margin: auto;
  --slide-height: 24rem;
  --slide-spacing: 1rem;
  --slide-size: 70%;
}

/* Modern Embla Carousel Styles */
.embla-modern {
  max-width: 100%;
  margin: auto;
  --slide-height: 24rem;
  --slide-spacing: 0.25rem; /* Even smaller gap between images */
  --slide-size: 300px; /* Fixed width for consistent sizing */
}

.embla__viewport {
  overflow: hidden;
}
.embla__container {
  display: flex;
  touch-action: pan-y pinch-zoom;
  margin-left: calc(var(--slide-spacing) * -1);
}
.embla__slide {
  transform: translate3d(0, 0, 0);
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding-left: var(--slide-spacing);
}

.embla__slide--modern {
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding-left: var(--slide-spacing);
}

.embla__slide__content {
  backface-visibility: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: var(--slide-height);
  user-select: none;
}
.embla__controls {
  display: grid;
  grid-template-columns: auto 1fr;
  justify-content: space-between;
  gap: 1.2rem;
  margin-top: 1.8rem;
}
.embla__buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.6rem;
  align-items: center;
}
.embla__button {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.5);
  -webkit-appearance: none;
  appearance: none;
  background-color: transparent;
  touch-action: manipulation;
  display: inline-flex;
  text-decoration: none;
  cursor: pointer;
  border: 0;
  padding: 0;
  margin: 0;
  box-shadow: inset 0 0 0 0.2rem #6b7280;
  width: 3.6rem;
  height: 3.6rem;
  z-index: 1;
  border-radius: 50%;
  color: #374151;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
}
.embla__button:disabled {
  color: #9ca3af;
}
.embla__button__svg {
  width: 35%;
  height: 35%;
}

/* Modern Carousel Button Styles */
.modern-carousel-button {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  -webkit-appearance: none;
  appearance: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  touch-action: manipulation;
  display: inline-flex;
  text-decoration: none;
  cursor: pointer;
  border: 0;
  padding: 0;
  margin: 0;
  width: 3rem;
  height: 3rem;
  z-index: 10;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
}

.modern-carousel-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.modern-carousel-button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
}

.modern-carousel-button:disabled {
  background: #e5e7eb;
  color: #9ca3af;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

/* Modern Text Catalogue Styles */
.catalogue-section {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.catalogue-button {
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.catalogue-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.catalogue-button:hover::before {
  left: 100%;
}

