import React, { useCallback, useEffect, useRef, useState } from 'react'
import {
  EmblaCarouselType,
  EmblaOptionsType
} from 'embla-carousel'
import useEmblaCarousel from 'embla-carousel-react'
import {
  NextButton,
  PrevButton,
  usePrevNextButtons
} from './EmblaCarouselArrowButtons'
import { DotButton, useDotButton } from './EmblaCarouselDotButton'
import { useTranslation } from '@/hooks/use-translation'
import { useLanguage } from '@/contexts/language-context'

type DocumentType = {
  path: string
  name: string
  category: string
}

type PropType = {
  documents: DocumentType[]
  options?: EmblaOptionsType
  onSlideChange?: (index: number) => void
  onApiReady?: (api: EmblaCarouselType, resetAutoplay: () => void) => void
}

const DocumentCarousel: React.FC<PropType> = (props) => {
  const { documents, options, onSlideChange, onApiReady } = props
  const { t } = useTranslation('homepage')
  const { locale } = useLanguage()
  const autoplayIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const [isSmallScreen, setIsSmallScreen] = useState(false)
  const [isVerySmallScreen, setIsVerySmallScreen] = useState(false)
  const [isClient, setIsClient] = useState(false)

  // Function to format document name for English version
  const formatDocumentName = (docName: string) => {
    const translatedName = t(`documentNames.${docName}`)

    // Only apply two-line formatting for English language
    if (locale === 'en') {
      const words = translatedName.split(' ')
      // If it's exactly 2 words, split into two lines
      if (words.length === 2) {
        return (
          <>
            <div>{words[0]}</div>
            <div>{words[1]}</div>
          </>
        )
      }
    }

    // For other languages or non-two-word names, return as single line
    return translatedName
  }

  const [emblaRef, emblaApi] = useEmblaCarousel(
    {
      loop: true,
      align: 'center',
      containScroll: false,
      ...options
    }
  )

  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi)

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick
  } = usePrevNextButtons(emblaApi)

  // Autoplay functionality
  const startAutoplay = useCallback(() => {
    if (autoplayIntervalRef.current) {
      clearInterval(autoplayIntervalRef.current)
    }
    autoplayIntervalRef.current = setInterval(() => {
      if (emblaApi) {
        emblaApi.scrollNext()
      }
    }, 3000)
  }, [emblaApi])

  const resetAutoplay = useCallback(() => {
    if (autoplayIntervalRef.current) {
      clearInterval(autoplayIntervalRef.current)
    }
    startAutoplay()
  }, [startAutoplay])

  // Handle client-side mounting and screen size detection
  useEffect(() => {
    setIsClient(true)

    const checkScreenSize = () => {
      const width = window.innerWidth
      setIsSmallScreen(width < 450)
      setIsVerySmallScreen(width < 440)
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)

    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  // Start autoplay when emblaApi is ready
  useEffect(() => {
    if (emblaApi) {
      startAutoplay()
      return () => {
        if (autoplayIntervalRef.current) {
          clearInterval(autoplayIntervalRef.current)
        }
      }
    }
  }, [emblaApi, startAutoplay])

  // Notify parent component when slide changes and provide API
  useEffect(() => {
    if (emblaApi) {
      if (onApiReady) {
        onApiReady(emblaApi, resetAutoplay)
      }
      if (onSlideChange) {
        const onSelect = () => {
          onSlideChange(emblaApi.selectedScrollSnap())
        }
        emblaApi.on('select', onSelect)
        onSelect() // Call once to set initial state
        return () => {
          emblaApi.off('select', onSelect)
        }
      }
    }
  }, [emblaApi, onSlideChange, onApiReady, resetAutoplay])





  return (
    <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* Carousel Container */}
      <div className="flex items-center min-h-[350px] sm:min-h-[400px] md:min-h-[500px] lg:min-h-[600px] xl:min-h-[650px] gap-1 sm:gap-2">
        {/* Left Arrow */}
        <div
          className="carousel-arrow-left flex-shrink-0 h-full flex items-center"
          style={{
            display: (typeof window !== 'undefined' && window.innerWidth < 440) || (isClient && isVerySmallScreen) ? 'none' : 'flex'
          }}
        >
          <div
            onClick={() => {
              onPrevButtonClick()
              resetAutoplay()
            }}
            className={`w-6 h-full xs:w-8 sm:w-10 md:w-12 lg:w-16 bg-transparent hover:bg-gray-200 rounded-lg flex items-center justify-center cursor-pointer transition-colors ${
              prevBtnDisabled ? 'opacity-30 cursor-not-allowed' : ''
            }`}
            style={{ minHeight: '180px' }}
          >
            <PrevButton onClick={() => {}} disabled={prevBtnDisabled} />
          </div>
        </div>

        {/* Carousel */}
        <div className="embla overflow-hidden flex-1 min-w-0 max-w-full" ref={emblaRef}>
          <div className="embla__container flex">
            {documents.map((doc, index) => {
              const isSelected = index === selectedIndex
              return (
                <div
                  className="embla__slide flex-[0_0_85%] xs:flex-[0_0_80%] sm:flex-[0_0_75%] md:flex-[0_0_65%] lg:flex-[0_0_55%] xl:flex-[0_0_45%] min-w-0 max-w-full px-0.5"
                  key={index}
                >
                  <div className="flex flex-col h-full max-w-full overflow-hidden">
                    <div
                      className="relative transition-all duration-300 ease-in-out max-w-full"
                      style={{
                        transform: isSelected
                          ? (isSmallScreen ? 'scale(0.64)' : 'scale(1)')
                          : (isSmallScreen ? 'scale(0.45)' : 'scale(0.7)'),
                        opacity: isSelected ? 1 : 0.5,
                        transformOrigin: 'center center',
                      }}
                    >
                      <div className="bg-white rounded-lg shadow-lg overflow-hidden border border-gray-200 max-w-full">
                        <div className="aspect-[3/4] xs:aspect-[3/4] sm:aspect-[4/5] p-0.5 sm:p-1 max-w-full overflow-hidden">
                          <img
                            src={doc.path}
                            alt={doc.name}
                            className="w-full h-full object-contain max-w-full"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Document type label - only show for selected image */}
                    {isSelected && (
                      // <div className="text-center mt-3 sm:mt-4 flex-shrink-0">
                      //   <h3 className="text-base xs:text-lg sm:text-xl md:text-3xl lg:text-2xl font-bold text-gray-700 leading-tight">
                      //     {formatDocumentName(doc.name)}
                      //   </h3>
                      // </div>
                    )}
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Right Arrow */}
        <div
          className="carousel-arrow-right flex-shrink-0 h-full flex items-center"
          style={{
            display: (typeof window !== 'undefined' && window.innerWidth < 440) || (isClient && isVerySmallScreen) ? 'none' : 'flex'
          }}
        >
          <div
            onClick={() => {
              onNextButtonClick()
              resetAutoplay()
            }}
            className={`w-6 h-full xs:w-8 sm:w-10 md:w-12 lg:w-16 bg-transparent hover:bg-gray-200 rounded-lg flex items-center justify-center cursor-pointer transition-colors ${
              nextBtnDisabled ? 'opacity-30 cursor-not-allowed' : ''
            }`}
            style={{ minHeight: '180px' }}
          >
            <NextButton onClick={() => {}} disabled={nextBtnDisabled} />
          </div>
        </div>
      </div>

      {/* Dots Navigation */}
      <div className="flex justify-center mt-4 gap-2">
        {scrollSnaps.map((_, index) => (
          <DotButton
            key={index}
            onClick={() => onDotButtonClick(index)}
            className={`touch-manipulation ${
              index === selectedIndex
                ? 'bg-gradient-to-r from-purple-600 via-blue-600 to-pink-600'
                : 'bg-gray-300 hover:bg-gray-400 active:bg-gray-500'
            }`}
          />
        ))}
      </div>
    </div>
  )
}

export default DocumentCarousel
