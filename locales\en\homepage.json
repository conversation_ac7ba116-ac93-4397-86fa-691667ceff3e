{"banner": {"limitedOffer": "🎉 Limited Offer: New sign-up users receive 10 free credits for trial!"}, "hero": {"title": {"line1": "Overwhelmed by the Mountain of", "line2": ""}, "documentTypes": ["Financial Reports", "Legal Documents", "Insurance Policies", "Request for Proposals", "Research Papers", "Compliance Documents"], "subtitle": "DocuChampAI is designed for busy people like you, making paperwork seamless, accurate and smart.", "description": "More Than Text: Our AI understands every part of your documents—images, digrams, complex tables, charts, and even handwriting.", "buttons": {"getStartedFree": "Get Started for Free", "viewPricing": "View Subscription Plans"}}, "videoSection": {"title": "See DocuChampAI in Action", "description": "Watch how our AI-powered document analysis transforms complex documents into actionable insights in minutes.", "loadingText": "Loading video...", "videoDescription": "This demonstration shows the complete workflow from document upload to AI analysis, highlighting key features like multimodal AI extraction, customizable templates, and automated report generation.", "muteButton": "Toggle sound"}, "valueProposition": {"heavyLifting": "Let AI handle the heavy lifting while you focus on what matters most.", "startSaving": "Start Saving Time Today"}, "trialSection": {"title": "Try DocuChampAI - Analyze your Document Instantly", "description": "Experience the power of AI document analysis without signing up. Upload a document and get instant insights, summaries, and recommendations."}, "whySection": {"title": "Why DocuChampAI?", "description": "See how our AI-powered document analysis platform outperforms traditional automation tools", "comparisonTable": {"headers": {"feature": "Feature", "others": "Others", "docuchamp": "DocuChampAI"}, "rows": {"dataUnderstanding": {"feature": "Data Understanding", "others": "Text only, many data loss", "docuchamp": "DocuChampAI reads everything, image, chart, table, text or even handwriting"}, "documentSize": {"feature": "Document Size", "others": "Small documents, <50 pages", "docuchamp": "Support files up to 300 pages, enabling deep insights from full-length materials"}, "useCase": {"feature": "Use Case", "others": "Chatbot interface, not practical for daily work, too many manual copy-and-paste", "docuchamp": "Designed for efficiency, output to .docx, .pdf directly without copy-and-paste"}}}}, "documentCarousel": {"title": "Streamlining Your Paperwork", "description": "Let our AI lighten your load—summarizing and extracting key points from insurance forms, receipts, contracts, and more, so you can breathe a little easier."}, "carousel": {"categories": {"contract": "Contract", "financial": "Financial", "generic": "Business", "handwritten": "Handwritten", "insurance": "Insurance", "manufacturing": "Manufacturing"}}, "documentNames": {"NDA": "NDA", "Employee": "Employee Contract", "Financial Report": "Financial Report", "Financial Statement": "Financial Statement", "Invoice": "Invoice", "Purchase Order": "Purchase Order", "RFP": "RFP", "Flowchart": "Handwritten Flowchart", "Form": "Handwritten Form", "Claim Form": "Claim Form", "Policy": "Insurance Policy", "Specification": "Manufacturing Spec"}, "aiFeatures": {"title": "AI That \"Sees\" Pictures, Charts, and Anything a Human Can", "description": "DocuChampAI doesn't just scan, it truly seeing pictures and patterns like a person would. Whether it's numbers, charts, tables or photos, our AI gives every element within your documents the attention.", "features": {"textAnalysis": {"title": "Text Analysis", "description": "Extract and analyze textual content with context understanding"}, "chartRecognition": {"title": "Chart Recognition", "description": "Identify and interpret charts, graphs, and visual data representations"}, "tableExtraction": {"title": "Table Extraction", "description": "Extract structured data from tables with high accuracy"}, "imageAnalysis": {"title": "Image Analysis", "description": "Analyze images, diagrams, and visual elements within documents"}}, "supportInfo": {"pages": "📄 Supports up to 300 pages", "formats": "📁 PDF, DOCX, PPTX formats"}, "featureList": {"chartAnalysis": {"title": "Chart Analysis", "description": "Understand financial charts, graphs, and visual data representations"}, "tableExtraction": {"title": "Table Extraction", "description": "Extract structured data from tables with high accuracy"}, "imageRecognition": {"title": "Image Recognition", "description": "Understand diagrams, photos, and visual elements within documents"}, "textAnalysis": {"title": "Text Analysis", "description": "Extract and analyze textual content with context understanding"}}}, "cta": {"title": "Ready to Transform Your Document Analysis?", "description": "Join thousands of users who are already using AI to analyze their documents smarter.", "button": "Get Started Today"}, "features": {"title": "What Makes DocuChampAI Speical", "scrollForMore": "Scroll for more", "wantMoreFeatures": "Want more features? Let us know!", "contactUs": "Contact Us", "explainingDiagrams": {"title": "Explaining Diagrams", "description": "Look at a diagram, charts or images and tell you what it means or how things are connected."}, "autoReportGeneration": {"title": "Auto Report Generation", "description": "Turn raw Excel data into formatted reports with charts, key metrics, and highlights."}, "handwritingReading": {"title": "Handwriting Reading", "description": "Turn handwritten notes, diagram and checkboxes into typed text."}, "translation": {"title": "Translation", "description": "Change document into any languages you need."}, "formChecking": {"title": "Form Checking", "description": "Make sure all required information is there and spot things that look wrong or missing."}, "mismatchFinding": {"title": "Mismatch Finding", "description": "Compare different documents to catch errors or differences."}, "documentSummarizing": {"title": "Document Summarizing", "description": "Quickly give you the main points from a big document."}, "autoDataEntry": {"title": "Auto-Data Entry", "description": "Take info from papers and put it where it belongs in the system."}, "mistakeFinding": {"title": "Mistake Finding", "description": "Find typos, missing info, or other errors, and suggest corrections."}, "keyInformationExtraction": {"title": "Key Information Extraction", "description": "Highlight people, companies, or important dates in documents."}, "contractAnalysis": {"title": "Contract Analysis", "description": "Point out things like end dates, payment terms, or penalty clauses."}, "steps": {"title": "Simple Steps to Get Started", "description": "Our intuitive interface makes document analysis effortless. Follow these simple steps to unlock powerful insights from your documents.", "step1": {"title": "Select Your Analysis Template", "description": "Choose from our professionally designed templates tailored for different document types and analysis needs. Whether you're analyzing financial reports, legal documents, or general business documents, our templates provide the perfect starting point for accurate and comprehensive analysis.", "features": ["Finance, Legal, Construction, Manufacturing and General templates", "Pre-configured analysis parameters", "Industry-specific insights"]}, "step2": {"title": "Choose Document Sections", "description": "Pick the specific sections of your document that you want to analyze for targeted insights. Our intelligent section detection helps you focus on the most relevant parts of your document, ensuring you get precise analysis results that matter most to your workflow.", "features": ["Intelligent section detection", "Custom section selection", "Focus on relevant content"]}, "step3": {"title": "Select Output Language", "description": "Choose your preferred language for the analysis output to ensure clear understanding and seamless integration with your workflow. Our multilingual support ensures that language barriers never limit your document analysis capabilities.", "features": ["200+ Multiple language support", "Accurate translations by GenAI", "Localized output format"]}, "step4": {"title": "Upload Document", "description": "Upload your document and let our AI analyze it. We support PDF, DOCX, PPTX files up to 300 pages. Our secure upload process ensures your documents are processed safely and efficiently.", "features": ["Secure large file upload", "Support multiple file formats", "Up to 300 pages"]}, "step5": {"title": "Discover Powerful Insights", "description": "Unlock comprehensive analysis results with intelligent insights that transform your documents into actionable intelligence. Experience seamless switching between preview modes to visualize your results exactly how you need them."}}, "videoToggle": {"preview": "Preview", "docx": "DOCX"}}}